/*
 * @Description:
 * @Author: xiuji
 * @Date: 2024-03-25 09:56:59
 * @LastEditTime: 2025-07-22 10:59:50
 * @LastEditors: xiuji <EMAIL>
 */
import { fileURLToPath, URL } from 'node:url';
import UnoCSS from 'unocss/vite';
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import AutoImport from "unplugin-auto-import/vite";
import Components from 'unplugin-vue-components/vite';
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers';
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers';

// https://vitejs.dev/config/
export default defineConfig({
  base: './', // 打包路径
  plugins: [
    vue(),
    UnoCSS(),
    AutoImport({
      imports: ["vue", "vue-router"],
      resolvers: [ElementPlusResolver()]
    }),
    Components({
      resolvers: [
        AntDesignVueResolver({
          importStyle: false, // css in js
        }),
        ElementPlusResolver()
      ],
    }),
  ],
  build: {
    rollupOptions: {
      output: {
        chunkFileNames: 'staticDir/js/[name]-[hash].js',
        entryFileNames: 'staticDir/js/[name]-[hash].js',
        assetFileNames: 'staticDir/[ext]/[name]-[hash].[ext]',
      }
    },
  },
  esbuild: {
    drop: ['console', 'debugger']
  },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      '@utils': fileURLToPath(new URL('./src/utils', import.meta.url)),
    }
  },
  css: {
    preprocessorOptions: {
      scss: {
        api: "modern-compiler",
        additionalData: "@import '@/assets/scss/common.scss';"
      }
    },
    devSourcemap: true
  },
  server: {
    hmr: {
      overlay: false
    },
    proxy: { // 本地开发环境通过代理实现跨域，生产环境使用 nginx 转发
      '/portal': {
        // target: 'http://192.168.10.102:8098', // 测试
        // target: 'http://192.168.100.45:8098', // jiarui
        // target: 'http://192.168.10.17:8098', // 亚龙
        // target: 'https://smart.jsisi.cn:8099',
        // target: 'http://36.133.172.156:81', // 开发环境
        // target: 'http://192.168.100.14:8098', // lzf
        // target: 'http://192.168.100.14:6098', // lzf
        // target: 'http://192.168.10.144:8098', // hwj
        // target: 'http://36.137.192.247:8081', // 开发环境
        // target: 'http://36.137.192.247:8082', // 开发环境
        // target: 'http://36.137.192.247:8088', // AI开发环境
        target: 'http://36.137.192.176:8087', // 预生产
        // target: 'http://192.168.3.12:8098', // 余智
        changeOrigin: true, //开启代理
        // rewrite: (path) => path.replace(/^\/portal/, '')
      }
    }
  },
})
