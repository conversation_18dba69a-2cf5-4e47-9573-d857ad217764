<template>
  <div class="component-test">
    <h2>组件拆分测试页面</h2>

    <div class="test-section">
      <h3>1. 生态厂商选择组件测试</h3>
      <EcoPartnerSelector
        :company-data="mockEcoPartnerData"
        :ecology-type="'2'"
        :selected-value="selectedEcoPartner"
        :reject-company-id-list="[]"
        :contact-select-mode="'userId'"
        @selection-change="handleEcoPartnerSelection"
        @contact-change="handleEcoPartnerContact"
        @company-detail="handleCompanyDetail"
        @add-partner="handleAddPartner"
      />
    </div>

    <div class="test-section">
      <h3>2. 自有能力方选择组件测试</h3>
      <OwnCapabilitySelector
        :own-person-data="mockOwnPersonData"
        :ecology-type="'1'"
        :selected-value="selectedOwnPerson"
        :reject-company-id-list="[]"
        @selection-change="handleOwnPersonSelection"
      />
    </div>

    <div class="test-section">
      <h3>3. 自由联系人选择组件测试</h3>
      <FreeContactSelector
        :contact-data="mockContactData"
        :selected-value="selectedContact"
        :reject-company-id-list="[]"
        :value-field="'belong'"
        @selection-change="handleContactSelection"
      />
    </div>

    <div class="test-controls">
      <h3>测试控制</h3>
      <div class="button-group">
        <a-button type="primary" @click="runIntegrationTest">运行集成测试</a-button>
        <a-button @click="clearTestResults">清空结果</a-button>
      </div>
    </div>

    <div class="test-results">
      <h3>测试结果</h3>
      <div class="results-summary">
        <p><strong>生态厂商选择:</strong> {{ selectedEcoPartner || '未选择' }}</p>
        <p><strong>自有能力方选择:</strong> {{ selectedOwnPerson || '未选择' }}</p>
        <p><strong>自由联系人选择:</strong> {{ selectedContact || '未选择' }}</p>
      </div>
      <div class="events-log">
        <h4>事件日志:</h4>
        <div class="log-container">
          <div v-for="(event, index) in testResults.events" :key="index" class="log-item">
            {{ event }}
          </div>
        </div>
      </div>
      <details class="raw-data">
        <summary>原始数据 (点击展开)</summary>
        <pre>{{ JSON.stringify(testResults, null, 2) }}</pre>
      </details>
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, ref } from 'vue'
import EcoPartnerSelector from './EcoPartnerSelector/index.vue'
import OwnCapabilitySelector from './OwnCapabilitySelector/index.vue'
import FreeContactSelector from './FreeContactSelector/index.vue'

export default defineComponent({
  name: 'ComponentTest',
  components: {
    EcoPartnerSelector,
    OwnCapabilitySelector,
    FreeContactSelector
  },
  setup() {
    const selectedEcoPartner = ref('')
    const selectedOwnPerson = ref('')
    const selectedContact = ref('')

    const testResults = reactive({
      ecoPartnerSelection: null,
      ownPersonSelection: null,
      contactSelection: null,
      events: []
    })

    // 模拟数据
    const mockEcoPartnerData = ref([{
      company: [
        {
          ecopartnerName: '测试生态厂商1',
          contactPhone: '13800138001',
          contactName: '张三',
          auth: 1,
          sync: 1,
          approve: 1,
          totalScore: 85.5,
          contactList: [
            { userId: '1', contactName: '张三', contactPhone: '13800138001', approve: 1 },
            { userId: '2', contactName: '李四', contactPhone: '13800138002', approve: 1 }
          ]
        },
        {
          ecopartnerName: '测试生态厂商2',
          contactPhone: '13800138003',
          contactName: '王五',
          auth: 1,
          sync: 1,
          approve: 1,
          introScore: 92.0,
          contactList: [
            { userId: '3', contactName: '王五', contactPhone: '13800138003', approve: 1 }
          ]
        }
      ]
    }])

    const mockOwnPersonData = ref([{
      ownPerson: [
        {
          belong: '技术部',
          contactName: '内部专家1',
          contactPhone: '13900139001',
          userId: 'own1'
        },
        {
          belong: '产品部',
          contactName: '内部专家2',
          contactPhone: '13900139002',
          userId: 'own2'
        }
      ]
    }])

    const mockContactData = ref([{
      ownProvince: [
        {
          belong: '省级联系人部门1',
          contactName: '省级联系人1',
          contactPhone: '13700137001',
          userId: 'contact1'
        },
        {
          belong: '省级联系人部门2',
          contactName: '省级联系人2',
          contactPhone: '13700137002',
          userId: 'contact2'
        }
      ]
    }])

    // 事件处理函数
    const handleEcoPartnerSelection = (e, company) => {
      selectedEcoPartner.value = e.target.value
      testResults.ecoPartnerSelection = { event: e.target.value, company }
      testResults.events.push(`生态厂商选择: ${company.ecopartnerName}`)
    }

    const handleEcoPartnerContact = (value, company) => {
      testResults.events.push(`生态厂商联系人变更: ${value} - ${company.ecopartnerName}`)
    }

    const handleCompanyDetail = (company) => {
      testResults.events.push(`查看公司详情: ${company.ecopartnerName}`)
    }

    const handleAddPartner = () => {
      testResults.events.push('新增生态厂商')
    }

    const handleOwnPersonSelection = (e, person) => {
      selectedOwnPerson.value = e.target.value
      testResults.ownPersonSelection = { event: e.target.value, person }
      testResults.events.push(`自有能力方选择: ${person.belong} - ${person.contactName}`)
    }

    const handleContactSelection = (e, contact) => {
      selectedContact.value = e.target.value
      testResults.contactSelection = { event: e.target.value, contact }
      testResults.events.push(`自由联系人选择: ${contact.belong} - ${contact.contactName}`)
    }

    // 运行集成测试
    const runIntegrationTest = () => {
      testResults.events.push('=== 开始集成测试 ===')

      // 测试数据验证
      const dataValidation = {
        ecoPartnerData: mockEcoPartnerData.value.length > 0,
        ownPersonData: mockOwnPersonData.value.length > 0,
        contactData: mockContactData.value.length > 0
      }

      testResults.events.push(`数据验证结果: ${JSON.stringify(dataValidation)}`)

      // 测试组件响应性
      setTimeout(() => {
        testResults.events.push('测试组件响应性...')

        // 模拟选择操作
        const mockEvent = { target: { value: '13800138001' } }
        const mockCompany = mockEcoPartnerData.value[0].company[0]
        handleEcoPartnerSelection(mockEvent, mockCompany)

        testResults.events.push('集成测试完成')
      }, 1000)
    }

    // 清空测试结果
    const clearTestResults = () => {
      testResults.ecoPartnerSelection = null
      testResults.ownPersonSelection = null
      testResults.contactSelection = null
      testResults.events = []
    }

    return {
      selectedEcoPartner,
      selectedOwnPerson,
      selectedContact,
      testResults,
      mockEcoPartnerData,
      mockOwnPersonData,
      mockContactData,
      handleEcoPartnerSelection,
      handleEcoPartnerContact,
      handleCompanyDetail,
      handleAddPartner,
      handleOwnPersonSelection,
      handleContactSelection,
      runIntegrationTest,
      clearTestResults
    }
  }
})
</script>

<style lang="scss" scoped>
.component-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background: #fafafa;
}

.test-controls {
  margin: 40px 0 20px 0;
  padding: 20px;
  background: #e6f7ff;
  border-radius: 8px;
  border: 1px solid #91d5ff;

  .button-group {
    display: flex;
    gap: 12px;
    margin-top: 12px;
  }
}

.test-results {
  margin-top: 20px;
  padding: 20px;
  background: #f0f0f0;
  border-radius: 8px;

  .results-summary {
    background: white;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;

    p {
      margin: 8px 0;
      font-size: 14px;
    }
  }

  .events-log {
    margin-bottom: 20px;

    h4 {
      margin-bottom: 10px;
      color: #333;
    }

    .log-container {
      background: white;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      max-height: 300px;
      overflow-y: auto;

      .log-item {
        padding: 8px 12px;
        border-bottom: 1px solid #f0f0f0;
        font-family: 'Courier New', monospace;
        font-size: 13px;

        &:last-child {
          border-bottom: none;
        }

        &:nth-child(odd) {
          background: #fafafa;
        }
      }
    }
  }

  .raw-data {
    summary {
      cursor: pointer;
      padding: 10px;
      background: #fafafa;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      margin-bottom: 10px;

      &:hover {
        background: #f0f0f0;
      }
    }

    pre {
      background: white;
      padding: 15px;
      border-radius: 4px;
      overflow-x: auto;
      font-size: 12px;
      border: 1px solid #d9d9d9;
    }
  }
}

h2, h3 {
  color: #333;
  margin-bottom: 16px;
}

h4 {
  color: #666;
  margin-bottom: 8px;
}
</style>
