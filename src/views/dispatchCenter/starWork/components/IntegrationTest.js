/**
 * 组件集成测试脚本
 * 用于验证拆分后的组件是否正常工作
 */

// 模拟数据生成器
export const generateMockData = () => {
  return {
    // 生态厂商数据
    ecoPartnerData: [{
      company: [
        {
          ecopartnerName: '华为技术有限公司',
          contactPhone: '13800138001',
          contactName: '张三',
          auth: 1,
          sync: 1,
          approve: 1,
          totalScore: 95.5,
          contactList: [
            { userId: '1001', contactName: '张三', contactPhone: '13800138001', approve: 1 },
            { userId: '1002', contactName: '李四', contactPhone: '13800138002', approve: 1 },
            { userId: '1003', contactName: '王五', contactPhone: '13800138003', approve: 0 }
          ]
        },
        {
          ecopartnerName: '腾讯科技有限公司',
          contactPhone: '13800138004',
          contactName: '赵六',
          auth: 1,
          sync: 1,
          approve: 1,
          introScore: 88.0,
          contactList: [
            { userId: '1004', contactName: '赵六', contactPhone: '13800138004', approve: 1 }
          ]
        },
        {
          ecopartnerName: '阿里巴巴集团',
          contactPhone: '13800138005',
          contactName: '孙七',
          auth: 0, // 未认证
          sync: 1,
          approve: 1,
          totalScore: 92.3,
          contactList: [
            { userId: '1005', contactName: '孙七', contactPhone: '13800138005', approve: 1 }
          ]
        }
      ]
    }],

    // 自有能力方数据
    ownPersonData: [{
      ownPerson: [
        {
          belong: '技术支持部',
          contactName: '内部专家A',
          contactPhone: '13900139001',
          userId: 'own001'
        },
        {
          belong: '产品研发部',
          contactName: '内部专家B',
          contactPhone: '13900139002',
          userId: 'own002'
        },
        {
          belong: '解决方案部',
          contactName: '内部专家C',
          contactPhone: '13900139003',
          userId: 'own003'
        }
      ]
    }],

    // 自由联系人数据
    contactData: [{
      ownProvince: [
        {
          belong: '省级技术支持中心',
          contactName: '省级专家1',
          contactPhone: '13700137001',
          userId: 'contact001'
        },
        {
          belong: '省级解决方案中心',
          contactName: '省级专家2',
          contactPhone: '13700137002',
          userId: 'contact002'
        }
      ]
    }]
  }
}

// 测试用例
export const testCases = {
  // 生态厂商组件测试
  ecoPartnerSelector: {
    // 测试搜索功能
    searchTest: {
      name: '搜索功能测试',
      steps: [
        '输入厂商名称进行搜索',
        '设置评分范围进行过滤',
        '验证搜索结果正确性',
        '测试重置功能'
      ]
    },
    
    // 测试分页功能
    paginationTest: {
      name: '分页功能测试',
      steps: [
        '验证分页显示正确',
        '测试页码切换',
        '测试每页显示数量变更',
        '验证总数计算正确'
      ]
    },
    
    // 测试选择功能
    selectionTest: {
      name: '选择功能测试',
      steps: [
        '测试厂商选择',
        '测试联系人选择',
        '验证禁用状态处理',
        '测试事件触发'
      ]
    }
  },

  // 自有能力方组件测试
  ownCapabilitySelector: {
    // 测试基本功能
    basicTest: {
      name: '基本功能测试',
      steps: [
        '验证数据正确显示',
        '测试选择功能',
        '验证禁用状态',
        '测试事件触发'
      ]
    }
  },

  // 自由联系人组件测试
  freeContactSelector: {
    // 测试基本功能
    basicTest: {
      name: '基本功能测试',
      steps: [
        '验证联系人信息显示',
        '测试选择功能',
        '验证value字段配置',
        '测试事件触发'
      ]
    }
  }
}

// 验证函数
export const validators = {
  // 验证组件props
  validateProps: (component, expectedProps) => {
    const issues = []
    
    expectedProps.forEach(prop => {
      if (!(prop in component.$props)) {
        issues.push(`缺少必需的prop: ${prop}`)
      }
    })
    
    return issues
  },

  // 验证事件发射
  validateEvents: (component, expectedEvents) => {
    const issues = []
    
    expectedEvents.forEach(event => {
      if (!component.$options.emits.includes(event)) {
        issues.push(`缺少必需的事件: ${event}`)
      }
    })
    
    return issues
  },

  // 验证数据结构
  validateDataStructure: (data, expectedStructure) => {
    const issues = []
    
    function checkStructure(obj, structure, path = '') {
      Object.keys(structure).forEach(key => {
        const currentPath = path ? `${path}.${key}` : key
        
        if (!(key in obj)) {
          issues.push(`缺少字段: ${currentPath}`)
          return
        }
        
        const expectedType = structure[key]
        const actualValue = obj[key]
        
        if (typeof expectedType === 'string') {
          if (typeof actualValue !== expectedType) {
            issues.push(`字段类型错误: ${currentPath}, 期望 ${expectedType}, 实际 ${typeof actualValue}`)
          }
        } else if (typeof expectedType === 'object' && expectedType !== null) {
          if (Array.isArray(expectedType)) {
            if (!Array.isArray(actualValue)) {
              issues.push(`字段类型错误: ${currentPath}, 期望数组, 实际 ${typeof actualValue}`)
            }
          } else {
            if (typeof actualValue === 'object' && actualValue !== null) {
              checkStructure(actualValue, expectedType, currentPath)
            } else {
              issues.push(`字段类型错误: ${currentPath}, 期望对象, 实际 ${typeof actualValue}`)
            }
          }
        }
      })
    }
    
    checkStructure(data, expectedStructure)
    return issues
  }
}

// 性能测试
export const performanceTests = {
  // 测试大数据量渲染
  testLargeDataRendering: (componentInstance, dataSize = 1000) => {
    const startTime = performance.now()
    
    // 生成大量测试数据
    const largeData = Array.from({ length: dataSize }, (_, index) => ({
      ecopartnerName: `测试厂商${index}`,
      contactPhone: `1380013${String(index).padStart(4, '0')}`,
      contactName: `联系人${index}`,
      auth: 1,
      sync: 1,
      approve: 1,
      totalScore: Math.random() * 100,
      contactList: [
        { userId: `${index}_1`, contactName: `联系人${index}_1`, approve: 1 }
      ]
    }))
    
    // 更新组件数据
    componentInstance.companyData = [{ company: largeData }]
    
    const endTime = performance.now()
    return {
      dataSize,
      renderTime: endTime - startTime,
      performance: endTime - startTime < 100 ? 'good' : 'needs optimization'
    }
  },

  // 测试搜索性能
  testSearchPerformance: (componentInstance, searchTerm) => {
    const startTime = performance.now()
    
    // 模拟搜索操作
    componentInstance.handleSearch({ ecopartnerName: searchTerm, minScore: '', maxScore: '' })
    
    const endTime = performance.now()
    return {
      searchTerm,
      searchTime: endTime - startTime,
      performance: endTime - startTime < 50 ? 'good' : 'needs optimization'
    }
  }
}

export default {
  generateMockData,
  testCases,
  validators,
  performanceTests
}
