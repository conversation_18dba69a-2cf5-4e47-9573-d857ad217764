<template>
  <el-table
    v-if="contactData && contactData.length > 0"
    :data="contactData"
    :empty-text="'暂无数据'"
    class="resize-table-header-line"
    :cell-style="{ textAlign: 'center' }"
    :header-cell-style="{ textAlign: 'center' }"
    border
    style="width: 100%; margin-top: 20px; margin-bottom: 20px"
  >
    <el-table-column
      prop="ownProvince"
      label="自有联系人"
    >
      <template #default="scope">
        <div class="contact-list">
          <div
            v-for="(contactItem, index) in scope.row.ownProvince"
            :key="index"
            class="box person-wrap"
          >
            <div class="contact-selection">
              <a-radio-group :value="selectedValue">
                <a-radio
                  :value="getContactValue(contactItem)"
                  @change="(e) => handleSelectionChange(e, contactItem)"
                  :disabled="isContactDisabled(contactItem)"
                />
              </a-radio-group>
              <span class="font-weight-500">
                {{ contactItem.belong }}
              </span>
            </div>

            <span class="contactName">
              {{ contactItem.contactName }}
            </span>

            <p class="contactPhone">
              {{ contactItem.contactPhone }}
            </p>
          </div>

          <div v-if="scope.row.ownProvince.length === 0" class="no-data">
            暂无数据
          </div>
        </div>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'FreeContactSelector',
  props: {
    // 联系人数据
    contactData: {
      type: Array,
      default: () => []
    },
    // 当前选中的值
    selectedValue: {
      type: String,
      default: ''
    },
    // 被拒绝的联系人ID列表
    rejectCompanyIdList: {
      type: Array,
      default: () => []
    },
    // 选择值的字段名 (contactPhone, belong 等)
    valueField: {
      type: String,
      default: 'belong'
    }
  },
  emits: ['selection-change'],
  setup(props, { emit }) {
    // 获取联系人的选择值
    const getContactValue = (contact) => {
      return contact[props.valueField] || contact.contactPhone
    }

    // 检查联系人是否被禁用
    const isContactDisabled = (contact) => {
      return props.rejectCompanyIdList.some(
        (value) => value.userId === contact.userId
      )
    }

    // 处理选择变化
    const handleSelectionChange = (e, contact) => {
      emit('selection-change', e, contact)
    }

    return {
      getContactValue,
      isContactDisabled,
      handleSelectionChange
    }
  }
})
</script>

<style lang="scss" scoped>
.contact-list {
  padding: 12px 0;
}

.box.person-wrap {
  width: max-content;
    min-width: 100%;
    padding: 0 38px;
    text-align: left;
    display: flex;
    align-items: center;
    gap: 5px;
    margin-bottom: 26px;
    white-space: nowrap;
    justify-content: space-between;

  &:last-of-type {
    margin-bottom: 0;
  }
}

.contact-selection {
  display: flex;
  width: 200px;
  align-items: center;
  gap: 8px;
}

.font-weight-500 {
  font-weight: 500;
}

.contactName {
  min-width: 100px;
  text-align: left;
}

.contactPhone {
  margin: 0;
  min-width: 120px;
  text-align: left;
  color: #666;
}

.no-data {
  text-align: center;
  color: #999;
  padding: 20px;
}
</style>
